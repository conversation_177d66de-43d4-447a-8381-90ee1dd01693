#!/usr/bin/env python3
"""
脚本用于从CSV文件中下载不同版本的封面对比图
- 每个category下面每个content_id是唯一的
- cover_id_v0和cover_id_v7的图下载后左右拼接为一张图
- ctr_0和ctr_7分别以0.1%的精度标注在各自图片的左上角
- 不同类别的图片保存在downloads不同子文件夹
"""

import os
import sys
from pathlib import Path
import pandas as pd
from afts import Afts
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO
import time
from tqdm import tqdm

# AFTS配置
endpoint_config = {
    "upload_endpoint_source": "mass.alipay.com",
    "download_endpoint_source": "mdn.alipayobjects.com",
    "authority_endpoint": "mmtcapi.alipay.com"
}
biz_key = "content_liveface"
biz_secret = "31cedb0f8bc24b778af865d8c5704705"

# 初始化AFTS客户端
_afts = Afts(
    biz_key=biz_key,
    biz_secret=biz_secret,
    endpoint_config=endpoint_config
)

def download_image_from_afts(afts_id):
    """
    从AFTS下载图片并返回PIL Image对象
    
    Args:
        afts_id (str): AFTS文件ID
    
    Returns:
        PIL.Image: 图片对象，失败时返回None
    """
    try:
        content = _afts.download_file(afts_id)
        image = Image.open(BytesIO(content))
        return image
    except Exception as e:
        print(f"下载图片失败 {afts_id}: {e}")
        return None

def add_ctr_text_to_image(image, ctr_value, position='left'):
    """
    在图片左上角添加CTR文本
    
    Args:
        image (PIL.Image): 图片对象
        ctr_value (float): CTR值
        position (str): 位置标识，用于调试
    
    Returns:
        PIL.Image: 添加文本后的图片
    """
    # 创建图片副本
    img_with_text = image.copy()
    draw = ImageDraw.Draw(img_with_text)
    
    # 格式化CTR值为0.1%精度
    ctr_text = f"{ctr_value:.1%}"
    
    # 尝试使用系统字体，如果失败则使用默认字体
    try:
        # 根据图片大小调整字体大小
        font_size = max(20, min(image.width // 15, image.height // 15))
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
    except:
        try:
            font = ImageFont.load_default()
        except:
            font = None
    
    # 计算文本位置（左上角，留一些边距）
    margin = 10
    text_x = margin
    text_y = margin
    
    # 添加背景矩形使文本更清晰
    if font:
        bbox = draw.textbbox((text_x, text_y), ctr_text, font=font)
    else:
        bbox = draw.textbbox((text_x, text_y), ctr_text)
    
    # 绘制半透明背景
    background_color = (0, 0, 0, 128)  # 半透明黑色
    draw.rectangle([bbox[0]-5, bbox[1]-2, bbox[2]+5, bbox[3]+2], fill=background_color)
    
    # 绘制白色文本
    text_color = (255, 255, 255)  # 白色
    if font:
        draw.text((text_x, text_y), ctr_text, fill=text_color, font=font)
    else:
        draw.text((text_x, text_y), ctr_text, fill=text_color)
    
    return img_with_text

def create_comparison_image(image_v0, image_v7, ctr_0, ctr_7):
    """
    创建对比图：左边v0，右边v7，各自标注CTR值
    
    Args:
        image_v0 (PIL.Image): v0版本图片
        image_v7 (PIL.Image): v7版本图片
        ctr_0 (float): v0的CTR值
        ctr_7 (float): v7的CTR值
    
    Returns:
        PIL.Image: 拼接后的对比图
    """
    # 确保两张图片高度一致，以较小的高度为准
    target_height = min(image_v0.height, image_v7.height)
    
    # 按比例调整图片大小
    v0_ratio = target_height / image_v0.height
    v7_ratio = target_height / image_v7.height
    
    new_v0_width = int(image_v0.width * v0_ratio)
    new_v7_width = int(image_v7.width * v7_ratio)
    
    image_v0_resized = image_v0.resize((new_v0_width, target_height), Image.Resampling.LANCZOS)
    image_v7_resized = image_v7.resize((new_v7_width, target_height), Image.Resampling.LANCZOS)
    
    # 在各自图片上添加CTR标注
    image_v0_with_text = add_ctr_text_to_image(image_v0_resized, ctr_0, 'left')
    image_v7_with_text = add_ctr_text_to_image(image_v7_resized, ctr_7, 'right')
    
    # 创建拼接图片
    total_width = new_v0_width + new_v7_width
    comparison_image = Image.new('RGB', (total_width, target_height), (255, 255, 255))
    
    # 粘贴两张图片
    comparison_image.paste(image_v0_with_text, (0, 0))
    comparison_image.paste(image_v7_with_text, (new_v0_width, 0))
    
    return comparison_image

def create_output_dir(base_dir, category):
    """创建输出目录"""
    output_dir = Path(base_dir) / category
    output_dir.mkdir(parents=True, exist_ok=True)
    return output_dir

def download_comparison_covers(csv_file, output_base_dir="downloads", max_downloads=None):
    """
    从CSV文件下载并创建对比封面图
    
    Args:
        csv_file (str): CSV文件路径
        output_base_dir (str): 输出基础目录
        max_downloads (int): 最大下载数量，None表示下载全部
    """
    print(f"开始从 {csv_file} 下载对比图...")
    print(f"文件保存基础目录: {output_base_dir}")
    
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file)
        print(f"原始数据行数: {len(df)}")
        
        # 按category分组，确保每个category下content_id唯一
        df_dedup = df.drop_duplicates(subset=['category', 'content_id'], keep='first')
        print(f"去重后数据行数: {len(df_dedup)}")
        
        # 如果指定了最大下载数量，则截取数据
        if max_downloads:
            df_dedup = df_dedup.head(max_downloads)
        
        total_rows = len(df_dedup)
        
        # 统计信息
        successful_downloads = 0
        failed_downloads = []
        category_stats = {}
        
        # 使用tqdm显示进度条
        with tqdm(total=total_rows, desc="下载进度", unit="行") as pbar:
            for _, row in df_dedup.iterrows():
                category = row['category']
                content_id = row['content_id']
                cover_id_v0 = row['cover_id_v0']
                cover_id_v7 = row['cover_id_v7']
                ctr_0 = float(row['ctr_0'])
                ctr_7 = float(row['ctr_7'])
                
                pbar.set_description(f"处理 {category}/{content_id[:15]}...")
                
                # 创建category对应的输出目录
                output_dir = create_output_dir(output_base_dir, category)
                
                # 初始化category统计
                if category not in category_stats:
                    category_stats[category] = {'success': 0, 'failed': 0}
                
                try:
                    # 下载两张图片
                    image_v0 = download_image_from_afts(cover_id_v0)
                    image_v7 = download_image_from_afts(cover_id_v7)
                    
                    if image_v0 is None or image_v7 is None:
                        failed_downloads.append(f"{category}/{content_id}: 图片下载失败")
                        category_stats[category]['failed'] += 1
                        pbar.update(1)
                        continue
                    
                    # 创建对比图
                    comparison_image = create_comparison_image(image_v0, image_v7, ctr_0, ctr_7)
                    
                    # 保存对比图
                    output_filename = f"{content_id}_comparison.jpg"
                    output_path = output_dir / output_filename
                    comparison_image.save(output_path, 'JPEG', quality=95)
                    
                    successful_downloads += 1
                    category_stats[category]['success'] += 1
                    
                except Exception as e:
                    failed_downloads.append(f"{category}/{content_id}: {str(e)}")
                    category_stats[category]['failed'] += 1
                
                pbar.update(1)
                
                # 添加延迟避免请求过于频繁
                time.sleep(0.1)
    
    except FileNotFoundError:
        print(f"错误: 找不到CSV文件 {csv_file}")
        return
    except Exception as e:
        print(f"处理CSV文件时出错: {e}")
        return
    
    # 输出统计信息
    print(f"\n=== 下载完成 ===")
    print(f"总处理行数: {total_rows}")
    print(f"成功创建对比图: {successful_downloads}")
    print(f"失败数量: {len(failed_downloads)}")
    
    print(f"\n=== 各类别统计 ===")
    for category, stats in category_stats.items():
        print(f"{category}: 成功 {stats['success']}, 失败 {stats['failed']}")
    
    if failed_downloads:
        print(f"\n失败的处理:")
        for failed in failed_downloads[:10]:  # 只显示前10个失败项
            print(f"  - {failed}")
        if len(failed_downloads) > 10:
            print(f"  ... 还有 {len(failed_downloads) - 10} 个失败项")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='下载并创建封面对比图')
    parser.add_argument('csv_file', help='CSV文件路径')
    parser.add_argument('-o', '--output', default='downloads',
                       help='输出基础目录 (默认: downloads)')
    parser.add_argument('-n', '--max-downloads', type=int,
                       help='最大下载数量限制')
    
    args = parser.parse_args()
    
    # 检查CSV文件是否存在
    if not os.path.exists(args.csv_file):
        print(f"错误: CSV文件不存在: {args.csv_file}")
        sys.exit(1)
    
    # 开始下载
    download_comparison_covers(args.csv_file, args.output, args.max_downloads)

if __name__ == "__main__":
    main()
