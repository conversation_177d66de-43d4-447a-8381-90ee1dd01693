#!/usr/bin/env python3
"""
统计csv/top1000.csv中ctt_id、cover_id和smart_cover_id的唯一数量
"""

import pandas as pd
import sys
import os

def count_unique_ids(csv_file_path):
    """
    统计CSV文件中ctt_id、cover_id和smart_cover_id的唯一数量

    Args:
        csv_file_path (str): CSV文件路径

    Returns:
        dict: 包含各个ID唯一数量的字典
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(csv_file_path):
            print(f"错误: 文件 {csv_file_path} 不存在")
            return None
            
        # 读取CSV文件
        print(f"正在读取文件: {csv_file_path}")
        df = pd.read_csv(csv_file_path)

        # 要统计的ID列
        id_columns = ['ctt_id', 'cover_id', 'smart_cover_id']

        # 检查哪些列存在
        available_columns = []
        missing_columns = []

        for col in id_columns:
            if col in df.columns:
                available_columns.append(col)
            else:
                missing_columns.append(col)

        if not available_columns:
            print(f"错误: CSV文件中没有找到任何目标列: {id_columns}")
            print(f"可用的列: {list(df.columns)}")
            return None

        if missing_columns:
            print(f"警告: 以下列不存在: {missing_columns}")

        # 统计总行数
        total_rows = len(df)
        print(f"总行数: {total_rows}")
        print("=" * 30)

        # 统计结果字典
        results = {}

        # 统计每个ID列的唯一数量
        for col in available_columns:
            unique_count = df[col].nunique()
            results[col] = unique_count
            print(f"{col}唯一数量: {unique_count}")

            # 统计重复数量
            duplicate_count = total_rows - unique_count
            if duplicate_count > 0:
                print(f"  重复的{col}数量: {duplicate_count}")

                # 显示重复的ID (前5个)
                duplicated_ids = df[df[col].duplicated(keep=False)][col].unique()
                if len(duplicated_ids) > 0:
                    print(f"  重复的{col}列表 (前5个): {duplicated_ids[:5].tolist()}")
            else:
                print(f"  没有重复的{col}")
            print("-" * 20)

        return results
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None

def main():
    """主函数"""
    csv_file_path = "csv/top2000.csv"

    print("=" * 50)
    print("CSV文件ID唯一数量统计工具")
    print("=" * 50)

    results = count_unique_ids(csv_file_path)

    if results is not None:
        print("=" * 50)
        print("统计结果汇总:")
        for id_type, count in results.items():
            print(f"  {id_type}唯一数量: {count}")
        print("=" * 50)
    else:
        print("统计失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
