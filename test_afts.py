from afts import Afts
from PIL import Image
from io import BytesIO

endpoint_config = {
            "upload_endpoint_source": "mass.alipay.com",
            "download_endpoint_source": "mdn.alipayobjects.com",
            "authority_endpoint": "mmtcapi.alipay.com"
        }
biz_key="content_liveface"
biz_secret="31cedb0f8bc24b778af865d8c5704705"


_afts = Afts(
            biz_key=biz_key,      # 业务标识
            biz_secret=biz_secret, # 业务密钥
            endpoint_config=endpoint_config # 环境配置
        )

def download_image_from_afts(afts_id):
    try:
        content = _afts.download_file(afts_id) # 下载图片内容
        return Image.open(BytesIO(content))
    except Exception as e:
        print(f"Error downloading image: {e}")
        return None
    
def download_video_from_afts(afts_id, save_path):
    """
    从AFTS下载视频并保存到指定路径

    Args:
        afts_id (str): AFTS文件ID
        save_path (str): 保存路径

    Returns:
        bool: 下载是否成功
    """
    try:
        content = _afts.download_file(afts_id)

        # 保存到指定路径
        with open(save_path, 'wb') as f:
            f.write(content)

        return True
    except Exception as e:
        return False

if __name__ == "__main__":
    # img = download_image_from_afts("A*WwpZTI6CBeQAAAAAAAAAAAAAfVx1AQ")
    # img.show()
    download_video_from_afts("A*M0hnQos120oAAAAAglAAAAgAfah3AA", "test.mp4")
