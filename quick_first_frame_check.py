#!/usr/bin/env python3
"""
快速检测脚本：判断封面是否为视频首帧
使用简单但有效的方法进行快速检测
"""

import os
import sys
import cv2
import numpy as np
from pathlib import Path
import argparse
from tqdm import tqdm
from PIL import Image
from skimage.metrics import structural_similarity as ssim

def extract_first_frame(video_path):
    """提取视频第一帧"""
    try:
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            return None
        ret, frame = cap.read()
        cap.release()
        return frame if ret else None
    except:
        return None

def load_cover_image(image_path):
    """加载封面图片"""
    try:
        pil_image = Image.open(image_path)
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')
        return cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    except:
        return None

def quick_similarity_check(img1, img2):
    """
    快速相似度检测
    使用SSIM (结构相似性指数) 进行比较，对压缩和亮度变化更鲁棒
    返回相似度分数 (0-1, 1表示完全相同)
    """
    # 调整到相同尺寸
    h1, w1 = img1.shape[:2]
    h2, w2 = img2.shape[:2]
    size = (min(w1, w2), min(h1, h2))

    img1_resized = cv2.resize(img1, size)
    img2_resized = cv2.resize(img2, size)

    # 转换为灰度
    gray1 = cv2.cvtColor(img1_resized, cv2.COLOR_BGR2GRAY)
    gray2 = cv2.cvtColor(img2_resized, cv2.COLOR_BGR2GRAY)

    # 使用SSIM计算结构相似性，对压缩和亮度变化更鲁棒
    try:
        similarity = ssim(gray1, gray2)
        return max(0, similarity)  # 确保不为负数
    except Exception as e:
        print(f"SSIM计算失败: {e}")
        return 0.0

def find_video_cover_pairs(downloads_dir):
    """找到视频和封面文件对"""
    downloads_path = Path(downloads_dir)
    pairs = []
    
    # 获取所有文件并按ctt_id分组
    files_by_ctt = {}
    for file_path in downloads_path.glob("*"):
        filename = file_path.name
        if '_' in filename:
            ctt_id = filename.split('_')[0]
            if ctt_id not in files_by_ctt:
                files_by_ctt[ctt_id] = {}
            
            if '_cover.jpg' in filename and '_smart_cover.jpg' not in filename:
                files_by_ctt[ctt_id]['cover'] = file_path
            elif '_video.mp4' in filename:
                files_by_ctt[ctt_id]['video'] = file_path
    
    # 找到同时有视频和封面的ctt_id
    for ctt_id, files in files_by_ctt.items():
        if 'cover' in files and 'video' in files:
            pairs.append({
                'ctt_id': ctt_id,
                'cover_path': files['cover'],
                'video_path': files['video']
            })
    
    return pairs

def batch_check_first_frames(downloads_dir, threshold=0.8, max_samples=None):
    """
    批量检测封面是否为视频首帧
    
    Args:
        downloads_dir: 下载目录
        threshold: 相似度阈值 (0-1)
        max_samples: 最大检测样本数
    """
    print("正在查找视频和封面文件对...")
    pairs = find_video_cover_pairs(downloads_dir)
    
    if not pairs:
        print("未找到匹配的视频和封面文件对")
        return
    
    print(f"找到 {len(pairs)} 对文件")
    
    if max_samples and len(pairs) > max_samples:
        pairs = pairs[:max_samples]
        print(f"限制检测数量为: {max_samples}")
    
    results = []
    first_frame_count = 0
    
    print(f"开始检测 (相似度阈值: {threshold})...")
    
    with tqdm(total=len(pairs), desc="检测进度") as pbar:
        for pair in pairs:
            ctt_id = pair['ctt_id']
            cover_path = pair['cover_path']
            video_path = pair['video_path']
            
            pbar.set_description(f"检测 {ctt_id[:15]}...")
            
            # 加载图片和视频首帧
            cover_img = load_cover_image(cover_path)
            first_frame = extract_first_frame(video_path)
            
            if cover_img is None or first_frame is None:
                result = {
                    'ctt_id': ctt_id,
                    'similarity': 0.0,
                    'is_first_frame': False,
                    'error': '无法加载文件'
                }
            else:
                # 计算相似度
                similarity = quick_similarity_check(cover_img, first_frame)
                is_first_frame = similarity >= threshold
                
                if is_first_frame:
                    first_frame_count += 1
                
                result = {
                    'ctt_id': ctt_id,
                    'similarity': round(similarity, 3),
                    'is_first_frame': is_first_frame,
                    'cover_file': cover_path.name,
                    'video_file': video_path.name
                }
            
            results.append(result)
            pbar.update(1)
    
    # 输出统计结果
    print(f"\n=== 检测完成 ===")
    print(f"总检测数量: {len(pairs)}")
    print(f"封面是首帧的数量: {first_frame_count}")
    print(f"首帧比例: {first_frame_count/len(pairs)*100:.1f}%")
    
    # 显示一些具体例子
    print(f"\n=== 相似度最高的前5个 ===")
    sorted_results = sorted([r for r in results if 'error' not in r], 
                          key=lambda x: x['similarity'], reverse=True)
    for i, result in enumerate(sorted_results[:5]):
        status = "✓ 是首帧" if result['is_first_frame'] else "✗ 不是首帧"
        print(f"{i+1}. {result['ctt_id']}: {result['similarity']:.3f} - {status}")
    
    print(f"\n=== 相似度最低的前5个 ===")
    for i, result in enumerate(sorted_results[-5:]):
        status = "✓ 是首帧" if result['is_first_frame'] else "✗ 不是首帧"
        print(f"{i+1}. {result['ctt_id']}: {result['similarity']:.3f} - {status}")
    
    return results

def single_check(cover_path, video_path):
    """检测单个封面和视频对"""
    print(f"检测封面: {cover_path}")
    print(f"检测视频: {video_path}")
    
    cover_img = load_cover_image(cover_path)
    first_frame = extract_first_frame(video_path)
    
    if cover_img is None:
        print("错误: 无法加载封面图片")
        return
    
    if first_frame is None:
        print("错误: 无法提取视频首帧")
        return
    
    similarity = quick_similarity_check(cover_img, first_frame)
    
    print(f"\n相似度分数: {similarity:.3f}")
    
    if similarity >= 0.9:
        print("结论: 封面很可能是视频首帧 (高度相似)")
    elif similarity >= 0.7:
        print("结论: 封面可能是视频首帧 (中等相似)")
    elif similarity >= 0.5:
        print("结论: 封面与首帧有一定相似性")
    else:
        print("结论: 封面不太可能是视频首帧 (相似度较低)")

def main():
    parser = argparse.ArgumentParser(description='快速检测封面是否为视频首帧')
    parser.add_argument('path', help='下载目录路径 或 封面图片路径')
    parser.add_argument('-v', '--video', help='视频文件路径 (单文件检测模式)')
    parser.add_argument('-t', '--threshold', type=float, default=0.9, 
                       help='相似度阈值 (默认: 0.9)')
    parser.add_argument('-n', '--max-samples', type=int, 
                       help='最大检测样本数')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.path):
        print(f"错误: 路径不存在: {args.path}")
        sys.exit(1)
    
    if args.video:
        # 单文件检测模式
        if not os.path.exists(args.video):
            print(f"错误: 视频文件不存在: {args.video}")
            sys.exit(1)
        single_check(args.path, args.video)
    else:
        # 批量检测模式
        if not os.path.isdir(args.path):
            print(f"错误: 需要提供目录路径进行批量检测")
            sys.exit(1)
        batch_check_first_frames(args.path, args.threshold, args.max_samples)

if __name__ == "__main__":
    main()
